# generated by fastapi-codegen:
#   filename:  openapi.yaml
#   timestamp: 2025-01-31T22:38:23+00:00

from __future__ import annotations

from typing import List, Optional, Union
from uuid import UUID

from fastapi import FastAPI
from fastapi.responses import HTMLResponse, PlainTextResponse
from fastapi.staticfiles import StaticFiles
import pathlib
import yaml

from batchprop.api.http.models import Artifact, BatchPropRequest, BatchPropResponse, PropagatorInfo

app = FastAPI(
    title='BatchProp Service',
    version='1.0.0',
)

# Mount static files directory
static_path = pathlib.Path(__file__).parent / "static"
app.mount("/static", StaticFiles(directory=str(static_path)), name="static")


@app.post(
    '/batchprop', response_model=None, responses={'202': {'model': BatchPropResponse}}
)
def post_batchprop(body: BatchPropRequest) -> Optional[BatchPropResponse]:
    """
    Submit a new batch propagation job
    """
    pass


@app.get('/batchprop/propagators', response_model=List[PropagatorInfo])
def get_batchprop_propagators() -> List[PropagatorInfo]:
    """
    List supported propagators
    """
    pass


@app.get('/batchprop/{run_id}/artifacts', response_model=List[Artifact])
def get_batchprop_run_id_artifacts(run_id: UUID) -> List[Artifact]:
    """
    Retrieve propagation result artifacts
    """
    pass


@app.get("/", response_class=HTMLResponse)
async def home():
    """
    Serve the homepage
    """
    template_path = pathlib.Path(__file__).parent / "templates" / "home.html"
    return template_path.read_text()


@app.get("/openapi.yaml", include_in_schema=False)
@app.get("/openapi.yml", include_in_schema=False)
async def get_openapi_yaml():
    """
    Serve the OpenAPI spec in YAML format
    """
    openapi_json = app.openapi()
    yaml_content = yaml.dump(openapi_json, sort_keys=False)
    return PlainTextResponse(yaml_content, media_type="text/yaml")
