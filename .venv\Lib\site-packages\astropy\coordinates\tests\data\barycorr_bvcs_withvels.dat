# def _get_barycorr_bvcs_withvels(coos, loc, injupyter=False):
#     """
#     Gets the barycentric correction of the test data from the
#     http://astroutils.astronomy.ohio-state.edu/exofast/barycorr.html web site.
#     Requires the https://github.com/tronsgaard/barycorr python interface to that
#     site.
#
#     Provided to reproduce the test data below.
#     """
#     import barycorr
#
#     from astropy.utils.console import ProgressBar
#
#     bvcs = []
#     for coo in ProgressBar(coos, ipython_widget=injupyter):
#         res = barycorr.bvc(
#             test_input_time.utc.jd,  # Global variable in test_valocity_corrs.py
#             coo.ra.deg,
#             coo.dec.deg,
#             lat=loc.geodetic[1].deg,
#             lon=loc.geodetic[0].deg,
#             pmra=coo.pm_ra_cosdec.to_value(u.mas / u.yr),
#             pmdec=coo.pm_dec.to_value(u.mas / u.yr),
#             parallax=coo.distance.to_value(u.mas, equivalencies=u.parallax()),
#             rv=coo.radial_velocity.to_value(u.m / u.s),
#             epoch=test_input_time.utc.jd,
#             elevation=loc.geodetic[2].to(u.m).value,
#         )
#         bvcs.append(res)
#     return bvcs * u.m / u.s
-10335.94926581
-14198.49117304
-2237.58656335
-14198.49078575
-17425.47883864
-17131.72711182
2424.38466675
2130.62819093
-17425.47834604
-19872.51254565
-24442.39064348
-11017.0964353
6978.07515501
11547.94831175
-1877.34560543
-19872.51188308
-21430.0931411
-27669.15919972
-16917.09482078
2729.57757823
16476.5087925
13971.97955641
-2898.04451551
-21430.09220144
-22028.52224227
-29301.93613248
-21481.14015151
-3147.44852058
14959.50849997
22232.91906264
14412.12044201
-3921.56783473
-22028.52088749
-21641.02117064
-29373.05982792
-24205.91319258
-8557.34473049
10250.50560918
23417.23357219
24781.98113432
13706.17025059
-4627.70468688
-21641.01928189
-20284.92926795
-28193.92117514
-22908.52127321
-6901.82512637
12336.45557256
25804.5137786
27200.49576347
15871.20847332
-2882.25080211
-20284.92696256
-18020.92824383
-25752.96528309
-20585.82211189
-4937.26088706
13870.58217495
27037.30698639
28402.0571686
17326.25314311
-1007.62313006
-18020.92552769
-14950.32653444
-22223.73793506
-14402.95155047
3930.72325162
22037.66749783
29311.07826101
21490.29193529
3156.62360741
-14950.32373745
-11210.52665171
-17449.59068509
-6697.54579192
12949.09948082
26696.01956077
24191.50403015
7321.50684816
-11210.52389393
-6968.87610888
-11538.7547047
1886.50525065
19881.64366561
24451.52197666
11026.26396455
-6968.87351156
-2415.17899385
-2121.44598968
17434.60465075
17140.87204017
-2415.1771038
2246.79688215
14207.61339552
2246.79790276
6808.43888253
