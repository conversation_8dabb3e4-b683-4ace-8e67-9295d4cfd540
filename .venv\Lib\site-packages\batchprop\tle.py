from dataclasses import dataclass
from datetime import datetime, timedelta, timezone

# TODO: use validatable pydantic class from OOE


@dataclass
class TLE:
    """Two Line Element set for describing orbital elements of a satellite.

    Attributes:
        line1 (str): First line of the TLE
        line2 (str): Second line of the TLE
        name (str, optional): Name/title of the satellite
    """
    line1: str
    line2: str
    line0: str = ""

    @property
    def epoch(self) -> datetime:
        """Extract the epoch (reference time) from the TLE.

        Returns:
            datetime: The epoch timestamp of the TLE in UTC
        """
        # Epoch year is in columns 19-20 of line1
        year = int(self.line1[18:20])
        # Adjust for century - TLEs use 2-digit years
        if year < 57:  # Sputnik launched in 1957, so assuming 2000+ for < 57
            year += 2000
        else:
            year += 1900

        # Epoch day with fraction is in columns 21-32
        day_with_fraction = float(self.line1[20:32])

        # Start with January 1st of the epoch year
        epoch = datetime(year, 1, 1, tzinfo=timezone.utc)

        # Add days (day of year - 1) because January 1st is already day 1
        day_of_year = int(day_with_fraction)
        epoch += timedelta(days=day_of_year - 1)

        # Add the fractional part of the day
        fraction_of_day = day_with_fraction - day_of_year
        epoch += timedelta(seconds=fraction_of_day *
                           86400)  # 86400 seconds in a day

        return epoch
