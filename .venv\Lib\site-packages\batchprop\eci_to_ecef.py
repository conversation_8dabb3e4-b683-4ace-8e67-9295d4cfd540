import numpy as np
from datetime import datetime
from typing import Protocol
import satkit as sk
from astropy.coordinates import ITRS, TEME,  CartesianRepresentation
from astropy.time import Time
import astropy.units as u
import orbitalopsengine as ooe
# import cupynumeric as cnp

from batchprop.batch import PropagatedTleBatch


class EciToEcefProtocol(Protocol):
    @staticmethod
    def transform_position(eci_pos: np.ndarray, eci_time: list[datetime]) -> np.ndarray:
        """Transform ECI position to ECEF position.

        Args:
            eci_pos: array of shape (n, 3) containing ECI position vectors in kilometers [x, y, z]
            eci_time: list of n UTC datetimes for the ECI positions

        Returns:
            array of shape (n, 3) containing ECEF position vectors in kilometers [x, y, z]
            where ret[i] is the ECEF position of the i-th ECI position at the i-th time
        """

    @staticmethod
    def transform_position_batch(eci_batch: PropagatedTleBatch) -> PropagatedTleBatch:
        """Transform a batch of ECI positions to ECEF positions.

        This method transforms a batch of position vectors from ECI (Earth-Centered Inertial)
        to ECEF (Earth-Centered Earth-Fixed) coordinates.

        Args:
            eci_batch: Batch containing ECI position vectors with shape (n_tles, n_times, 3)
                      and associated metadata (TLEs and times)

        Returns:
            Batch containing ECEF position vectors with the same shape and metadata
        """
        ...


class EciToEcefAstropy(EciToEcefProtocol):
    @staticmethod
    def _transform_core(positions: np.ndarray, times: Time) -> np.ndarray:
        """Core transformation logic shared between transform methods.

        Args:
            positions: Array of ECI positions with units in kilometers
            times: Astropy Time object(s) for the transformation

        Returns:
            Array of ECEF positions in kilometers
        """
        # Convert positions to Astropy units (km) and split into x, y, z components
        eci_pos_with_units = positions * u.km
        x = eci_pos_with_units[:, 0]
        y = eci_pos_with_units[:, 1]
        z = eci_pos_with_units[:, 2]

        # Create CartesianRepresentation for all positions
        eci_cartesian = CartesianRepresentation(x, y, z)

        # Convert from TEME (ECI) to ITRS (ECEF)
        gcrs = TEME(eci_cartesian, obstime=times)
        itrs = gcrs.transform_to(ITRS(obstime=times))

        # Extract cartesian coordinates and convert back to numpy array
        return np.column_stack([
            itrs.x.to(u.km).value,
            itrs.y.to(u.km).value,
            itrs.z.to(u.km).value
        ])

    @staticmethod
    def transform_position(eci_pos: np.ndarray, eci_time: list[datetime]) -> np.ndarray:
        """Transform ECI position to ECEF position using Astropy.

        Args:
            eci_pos: array of shape (n, 3) containing ECI position vectors in kilometers [x, y, z]
            eci_time: list of n UTC datetimes for the ECI positions

        Returns:
            array of shape (n, 3) containing ECEF position vectors in kilometers [x, y, z]
            where ret[i] is the ECEF position of the i-th ECI position at the i-th time.
            Any input row containing NaN values will result in NaN values in the corresponding output row.
        """
        # Find rows with any NaN values
        nan_mask = np.isnan(eci_pos).any(axis=1)

        # If all values are NaN, return array of NaNs
        if np.all(nan_mask):
            return np.full_like(eci_pos, np.nan)

        # If there are some NaN values, process only valid rows
        if np.any(nan_mask):
            valid_pos = eci_pos[~nan_mask]
            valid_times = [t for i, t in enumerate(
                eci_time) if not nan_mask[i]]
        else:
            valid_pos = eci_pos
            valid_times = eci_time

        # Convert times to Astropy Time objects
        times = Time(valid_times)

        # Use core transformation logic
        valid_ecef_pos = EciToEcefAstropy._transform_core(valid_pos, times)

        # If there were any NaN values, reconstruct the full output array
        if np.any(nan_mask):
            ecef_pos = np.full_like(eci_pos, np.nan)
            ecef_pos[~nan_mask] = valid_ecef_pos
            return ecef_pos

        return valid_ecef_pos

    @staticmethod
    def transform_position_batch(eci_batch: PropagatedTleBatch) -> PropagatedTleBatch:
        """Transform a batch of ECI positions to ECEF positions using Astropy.

        This optimized implementation:
        1. Handles all TLEs in a vectorized way when possible
        2. Efficiently processes NaN values
        3. Reduces redundant computations

        Args:
            eci_batch: Batch containing ECI position vectors with shape (n_tles, n_times, 3)
                      and associated metadata (TLEs and times)

        Returns:
            Batch containing ECEF position vectors with the same shape and metadata
        """
        tles = eci_batch.tles
        times = eci_batch.times
        eci_positions = eci_batch.data

        n_tles = len(tles)
        n_times = len(times)

        # Create a mask for NaN values in the input positions
        nan_mask = np.isnan(eci_positions).any(axis=2)

        # Create output array with NaNs
        ecef_positions = np.full((n_tles, n_times, 3), np.nan)

        # If all values are NaN, return array of NaNs
        if np.all(nan_mask):
            return PropagatedTleBatch(
                data=ecef_positions,
                tles=tles,
                times=times
            )

        # Convert times to Astropy Time objects once
        astropy_times = Time(times)

        # Process each time slice (all TLEs at once for each time)
        for t_idx in range(n_times):
            # Skip if all positions at this time are NaN
            if np.all(nan_mask[:, t_idx]):
                continue

            # Get valid TLE indices for this time
            valid_tle_indices = np.where(~nan_mask[:, t_idx])[0]

            # Get positions for valid TLEs at this time
            valid_positions = eci_positions[valid_tle_indices, t_idx]

            # Use core transformation logic with the current time for all positions
            current_time = astropy_times[t_idx]
            valid_ecef = EciToEcefAstropy._transform_core(
                valid_positions, current_time)

            # Store results in output array
            ecef_positions[valid_tle_indices, t_idx] = valid_ecef

        # Create a new batch with the transformed positions but same metadata
        return PropagatedTleBatch(
            data=ecef_positions,
            tles=tles,
            times=times
        )


class EciToEcefSatkit(EciToEcefProtocol):
    @staticmethod
    def _transform_core(positions: np.ndarray, rotation_matrices: np.ndarray) -> np.ndarray:
        """Core transformation logic shared between transform methods.

        Args:
            positions: Array of ECI positions in kilometers
            rotation_matrices: Array of rotation matrices from ECI to ECEF

        Returns:
            Array of ECEF positions in kilometers
        """
        # Apply rotation to each position
        return np.einsum('nij,nj->ni', rotation_matrices, positions)

    @staticmethod
    def transform_position(eci_pos: np.ndarray, eci_time: list[datetime]) -> np.ndarray:
        """Transform ECI position to ECEF position using SatKit.

        Args:
            eci_pos: array of shape (n, 3) containing ECI position vectors in kilometers [x, y, z]
            eci_time: list of n UTC datetimes for the ECI positions

        Returns:
            array of shape (n, 3) containing ECEF position vectors in kilometers [x, y, z]
            where ret[i] is the ECEF position of the i-th ECI position at the i-th time.
            Any input row containing NaN values will result in NaN values in the corresponding output row.
        """
        # Find rows with any NaN values
        nan_mask = np.isnan(eci_pos).any(axis=1)

        # If all values are NaN, return array of NaNs
        if np.all(nan_mask):
            return np.full_like(eci_pos, np.nan)

        # If there are some NaN values, process only valid rows
        if np.any(nan_mask):
            valid_pos = eci_pos[~nan_mask]
            valid_times = [t for i, t in enumerate(
                eci_time) if not nan_mask[i]]
        else:
            valid_pos = eci_pos
            valid_times = eci_time

        # Get rotation matrices for each valid time
        rotation_matrices = np.stack([
            sk.frametransform.qteme2itrf(
                sk.time.from_datetime(t)).as_rotation_matrix()
            for t in valid_times
        ])

        # Use core transformation logic
        valid_ecef_pos = EciToEcefSatkit._transform_core(
            valid_pos, rotation_matrices)

        # If there were any NaN values, reconstruct the full output array
        if np.any(nan_mask):
            ecef_pos = np.full_like(eci_pos, np.nan)
            ecef_pos[~nan_mask] = valid_ecef_pos
            return ecef_pos

        return valid_ecef_pos

    @staticmethod
    def transform_position_batch(eci_batch: PropagatedTleBatch) -> PropagatedTleBatch:
        """Transform a batch of ECI positions to ECEF positions using Satkit.

        This optimized implementation:
        1. Pre-computes rotation matrices for all times once
        2. Uses vectorized operations to transform all positions at once
        3. Handles NaN values efficiently

        Args:
            eci_batch: Batch containing ECI position vectors with shape (n_tles, n_times, 3)
                      and associated metadata (TLEs and times)

        Returns:
            Batch containing ECEF position vectors with the same shape and metadata
        """
        tles = eci_batch.tles
        times = eci_batch.times
        eci_positions = eci_batch.data

        n_tles = len(tles)
        n_times = len(times)

        # Pre-compute rotation matrices for all times once
        rotation_matrices = np.stack([
            sk.frametransform.qteme2itrf(
                sk.time.from_datetime(t)).as_rotation_matrix()
            for t in times
        ])

        # Create a mask for NaN values in the input positions
        nan_mask = np.isnan(eci_positions).any(axis=2)

        # Create output array with NaNs
        ecef_positions = np.full_like(eci_positions, np.nan)

        # Find indices of non-NaN positions
        valid_indices = np.where(~nan_mask)

        # Get valid input positions
        valid_positions = eci_positions[valid_indices]

        # Get corresponding rotation matrices (for each valid time index)
        time_indices = valid_indices[1]
        rotation_matrices_for_valid = rotation_matrices[time_indices]

        # Use core transformation logic
        valid_ecef_positions = EciToEcefSatkit._transform_core(
            valid_positions, rotation_matrices_for_valid)

        # Put the results back in the output array
        ecef_positions[valid_indices] = valid_ecef_positions

        # Create a new batch with the transformed positions but same metadata
        return PropagatedTleBatch(
            data=ecef_positions,
            tles=tles,
            times=times
        )


class EciToEcefOrbitalOpsEngine(EciToEcefProtocol):
    # Import and alias OOE types
    from orbitalopsengine.physics.quantities.vector3d import Vector3D
    from orbitalopsengine.physics.reference_frames.eci_ecef import ECItoECEFTransformation
    from orbitalopsengine.physics.quantities.quantities import kilometer
    from orbitalopsengine.physics.reference_frames.reference_frame import ECI

    @staticmethod
    def _transform_core(position_eci: np.ndarray, time: datetime, transformation: 'ECItoECEFTransformation') -> np.ndarray:
        """Core transformation logic shared between transform methods.

        Args:
            position_eci: ECI position vector in kilometers [x, y, z]
            time: UTC datetime for the transformation
            transformation: OOE transformation object

        Returns:
            ECEF position vector in kilometers [x, y, z]
        """
        # Create OOE Vector3D from the ECI position
        ooe_position_eci = EciToEcefOrbitalOpsEngine.Vector3D(
            tuple(position_eci),
            unit=EciToEcefOrbitalOpsEngine.kilometer,
            reference_frame=EciToEcefOrbitalOpsEngine.ECI
        )

        # Transform to ECEF using the provided time
        ooe_position_ecef = transformation.apply_given_time(
            ooe_position_eci,
            time
        )

        # Return ECEF position as numpy array
        return np.array([
            ooe_position_ecef.x,
            ooe_position_ecef.y,
            ooe_position_ecef.z
        ])

    @staticmethod
    def transform_position(eci_pos: np.ndarray, eci_time: list[datetime]) -> np.ndarray:
        """Transform ECI position to ECEF position using OrbitalOpsEngine.

        Args:
            eci_pos: array of shape (n, 3) containing ECI position vectors in kilometers [x, y, z]
            eci_time: list of n UTC datetimes for the ECI positions

        Returns:
            array of shape (n, 3) containing ECEF position vectors in kilometers [x, y, z]
            where ret[i] is the ECEF position of the i-th ECI position at the i-th time.
            Any input row containing NaN values will result in NaN values in the corresponding output row.
        """
        # Find rows with any NaN values
        nan_mask = np.isnan(eci_pos).any(axis=1)

        # Initialize output array with NaNs
        ecef_pos = np.full_like(eci_pos, np.nan)

        # If all values are NaN, return array of NaNs
        if np.all(nan_mask):
            return ecef_pos

        # Create transformation object once
        eci_to_ecef_transformation = EciToEcefOrbitalOpsEngine.ECItoECEFTransformation()

        # Process only non-NaN positions
        valid_indices = np.where(~nan_mask)[0]
        for i in valid_indices:
            # Use core transformation logic
            ecef_pos[i] = EciToEcefOrbitalOpsEngine._transform_core(
                eci_pos[i],
                eci_time[i],
                eci_to_ecef_transformation
            )

        return ecef_pos

    @staticmethod
    def transform_position_batch(eci_batch: PropagatedTleBatch) -> PropagatedTleBatch:
        """Transform a batch of ECI positions to ECEF positions using OrbitalOpsEngine.

        This optimized implementation:
        1. Creates the transformation object only once
        2. Processes data in time slices to avoid redundant calculations
        3. Efficiently handles NaN values

        Args:
            eci_batch: Batch containing ECI position vectors with shape (n_tles, n_times, 3)
                      and associated metadata (TLEs and times)

        Returns:
            Batch containing ECEF position vectors with the same shape and metadata
        """
        tles = eci_batch.tles
        times = eci_batch.times
        eci_positions = eci_batch.data

        n_tles = len(tles)
        n_times = len(times)

        # Create a mask for NaN values in the input positions
        nan_mask = np.isnan(eci_positions).any(axis=2)

        # Create output array with NaNs
        ecef_positions = np.full_like(eci_positions, np.nan)

        # If all values are NaN, return early
        if np.all(nan_mask):
            return PropagatedTleBatch(
                data=ecef_positions,
                tles=tles,
                times=times
            )

        # Create transformation object once
        eci_to_ecef_transformation = EciToEcefOrbitalOpsEngine.ECItoECEFTransformation()

        # Process each time once (with all valid TLEs for that time)
        for t_idx, time in enumerate(times):
            # Skip if all positions at this time are NaN
            if np.all(nan_mask[:, t_idx]):
                continue

            # Get valid TLE indices for this time
            valid_tle_indices = np.where(~nan_mask[:, t_idx])[0]

            # Process each valid TLE for this time
            for tle_idx in valid_tle_indices:
                # Use core transformation logic
                ecef_positions[tle_idx, t_idx] = EciToEcefOrbitalOpsEngine._transform_core(
                    eci_positions[tle_idx, t_idx],
                    time,
                    eci_to_ecef_transformation
                )

        # Create a new batch with the transformed positions but same metadata
        return PropagatedTleBatch(
            data=ecef_positions,
            tles=tles,
            times=times
        )


# class EciToEcefLegate(EciToEcefProtocol):
#     @staticmethod
#     def _transform_core(positions: cnp.ndarray, rotation_matrices: cnp.ndarray) -> cnp.ndarray:
#         """Core transformation logic using cupynumeric for GPU acceleration.

#         Args:
#             positions: Array of ECI positions in kilometers
#             rotation_matrices: Array of rotation matrices from ECI to ECEF
#                 Can be either a single 3x3 matrix or batch of matrices

#         Returns:
#             Array of ECEF positions in kilometers
#         """
#         # Standard matrix multiplication: R · p
#         # No special cases needed - einsum handles all cases correctly
#         return cnp.einsum('...ij,...j->...i', rotation_matrices, positions)

#     @staticmethod
#     def transform_position(eci_pos: np.ndarray, eci_time: list[datetime]) -> np.ndarray:
#         """Transform ECI position to ECEF position using Legate cupynumeric for GPU acceleration.

#         Args:
#             eci_pos: array of shape (n, 3) containing ECI position vectors in kilometers [x, y, z]
#             eci_time: list of n UTC datetimes for the ECI positions

#         Returns:
#             array of shape (n, 3) containing ECEF position vectors in kilometers [x, y, z]
#             where ret[i] is the ECEF position of the i-th ECI position at the i-th time.
#             Any input row containing NaN values will result in NaN values in the corresponding output row.
#         """
#         # Find rows with any NaN values
#         nan_mask = np.isnan(eci_pos).any(axis=1)
        
#         # If all values are NaN, return array of NaNs
#         if np.all(nan_mask):
#             return np.full_like(eci_pos, np.nan)
        
#         # If there are some NaN values, process only valid rows
#         if np.any(nan_mask):
#             valid_pos = eci_pos[~nan_mask]
#             valid_times = [t for i, t in enumerate(eci_time) if not nan_mask[i]]
#         else:
#             valid_pos = eci_pos
#             valid_times = eci_time
        
#         # Convert positions to cupynumeric array
#         valid_pos_gpu = cnp.array(valid_pos)
        
#         # Get rotation matrices exactly like other implementations
#         # This ensures consistency with reference implementations
#         rotation_matrices_list = [
#             sk.frametransform.qteme2itrf(sk.time.from_datetime(t)).as_rotation_matrix()
#             for t in valid_times
#         ]
        
#         # Move computation to GPU
#         rotation_matrices = cnp.array(rotation_matrices_list)
        
#         # Apply transformation to each position with its corresponding rotation matrix
#         valid_ecef_pos_gpu = EciToEcefLegate._transform_core(valid_pos_gpu, rotation_matrices)
        
#         # Convert result back to CPU
#         valid_ecef_pos = valid_ecef_pos_gpu.get() if hasattr(valid_ecef_pos_gpu, 'get') else np.array(valid_ecef_pos_gpu)
        
#         # If there were any NaN values, reconstruct the full output array
#         if np.any(nan_mask):
#             ecef_pos = np.full_like(eci_pos, np.nan)
#             ecef_pos[~nan_mask] = valid_ecef_pos
#             return ecef_pos
        
#         return valid_ecef_pos

#     @staticmethod
#     def transform_position_batch(eci_batch: PropagatedTleBatch) -> PropagatedTleBatch:
#         """Transform a batch of ECI positions to ECEF positions using Legate cupynumeric for GPU acceleration.

#         This implementation ensures consistent results with other transformers while 
#         leveraging GPU acceleration.

#         Args:
#             eci_batch: Batch containing ECI position vectors with shape (n_tles, n_times, 3)
#                       and associated metadata (TLEs and times)

#         Returns:
#             Batch containing ECEF position vectors with the same shape and metadata
#         """
#         tles = eci_batch.tles
#         times = eci_batch.times
        
#         # Create a mask for NaN values in the input positions
#         nan_mask = np.isnan(eci_batch.data).any(axis=2)
        
#         # Create output array with NaNs
#         ecef_positions = np.full_like(eci_batch.data, np.nan)
        
#         # Skip processing if all values are NaN
#         if np.all(nan_mask):
#             return PropagatedTleBatch(
#                 data=ecef_positions,
#                 tles=tles,
#                 times=times
#             )
        
#         # Pre-compute rotation matrices for all times once
#         # Use reference implementation approach for consistency
#         rotation_matrices_list = [
#             sk.frametransform.qteme2itrf(sk.time.from_datetime(t)).as_rotation_matrix()
#             for t in times
#         ]
        
#         # Convert to GPU for acceleration
#         rotation_matrices = cnp.array(rotation_matrices_list)
        
#         # Process each TLE individually to ensure same results as individual transforms
#         for i in range(len(tles)):
#             # Skip completely invalid TLEs
#             if np.all(nan_mask[i]):
#                 continue
                
#             # Process valid time points for this TLE
#             valid_indices = np.where(~nan_mask[i])[0]
            
#             # Get positions and corresponding rotation matrices
#             valid_positions = cnp.array(eci_batch.data[i, valid_indices])
#             valid_rotations = rotation_matrices[valid_indices]
            
#             # Transform positions using GPU acceleration
#             valid_ecef = EciToEcefLegate._transform_core(valid_positions, valid_rotations)
            
#             # Convert back to CPU and store
#             valid_ecef_cpu = valid_ecef.get() if hasattr(valid_ecef, 'get') else np.array(valid_ecef)
#             ecef_positions[i, valid_indices] = valid_ecef_cpu
        
#         # Return batch with transformed positions
#         return PropagatedTleBatch(
#             data=ecef_positions,
#             tles=tles,
#             times=times
#         )
