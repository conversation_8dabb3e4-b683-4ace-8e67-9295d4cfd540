from typing import TypeAlias
import omnicat.ingestor.udl.models as udl_models
from omnicat.ingestor.udl.models.elset import IdElset, ClassificationMarking, Line, Origin, Source, OrigObjectId, Algorithm, OrigNetwork, DataMode, SourcedDataType
from pydantic import BaseModel
from datetime import datetime
from uuid import UUID

Elset_Full: TypeAlias = udl_models.Elset_Full

# TODO: satno should be a string
# TODO: field descriptions!


class Elset_Sgp4(BaseModel):
    idElset: IdElset
    satNo: int | str
    epoch: datetime
    dataMode: DataMode
    source: Source
    classificationMarking: ClassificationMarking

    def getIdOnOrbit(self) -> str | None:
        if self.satNo is not None:
            return str(self.satNo)
        return None


class Elset_UctCandidate(BaseModel):
    idElset: IdElset
    classificationMarking: ClassificationMarking
    satNo: int | str | None = None
    epoch: datetime
    meanMotion: float
    idOnOrbit: str | None = None
    eccentricity: float
    inclination: float
    raan: float
    argOfPerigee: float
    meanAnomaly: float
    revNo: int
    bStar: float
    meanMotionDot: float
    meanMotionDDot: float
    semiMajorAxis: float
    period: float
    apogee: float
    perigee: float
    line1: Line
    line2: Line
    sourcedData: list[str]
    sourcedDataTypes: list[SourcedDataType]
    createdBy: str | None = None
    source: Source
    dataMode: DataMode
    algorithm: Algorithm
    origNetwork: OrigNetwork | None = None

    def getIdOnOrbit(self) -> str | None:
        return self.idOnOrbit


class Elset_Sgp4Xp(BaseModel):
    idElset: IdElset
    classificationMarking: ClassificationMarking
    satNo: int | str
    epoch: str
    line1: Line
    line2: Line
    createdAt: str
    createdBy: str
    origin: Origin
    source: Source
    ephemType: int
    uct: bool
    origObjectId: OrigObjectId
    dataMode: str
    algorithm: Algorithm
    origNetwork: OrigNetwork

    def getIdOnOrbit(self) -> str | None:
        if self.origObjectId is not None:
            return str(self.origObjectId)
        return None
