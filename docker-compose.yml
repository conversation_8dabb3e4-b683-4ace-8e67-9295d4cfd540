services:
  neo4j:
    image: neo4j:5.15
    environment:
      - NEO4J_AUTH=neo4j/password
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
      - NEO4J_apoc_import_file_use__neo4j__config=true
    ports:
      - "7474:7474"
      - "7687:7687"
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/import
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:7474 || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5

  postgis:
    image: postgis/postgis
    restart: always
    environment:
      - POSTGRES_DB=gis
      - POSTGRES_USER=gis
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgis_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U gis"]
      interval: 10s
      timeout: 5s
      retries: 5

  omnicat-api:
    image: omnicat-api:latest
    build:
      context: .
      dockerfile: Dockerfile
      target: api
      ssh:
        - default=C:/Users/<USER>/.ssh/id_rsa  # Adjust this path to your SSH key location
    environment:
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=password
      - NEO4J_URI=bolt://neo4j:7687
      - POSTGIS_USER=gis
      - POSTGIS_PASSWORD=password
      - POSTGIS_HOST=postgis
      - POSTGIS_PORT=5432
    ports:
      - "8000:8000"
    depends_on:
      neo4j:
        condition: service_healthy
      postgis:
        condition: service_healthy

  # omnicat-udl-ingestor:
  #   image: omnicat-udl-ingestor:latest
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #     target: udl-ingestor 
  #     ssh:
  #       - default=C:/Users/<USER>/.ssh/id_rsa  # Adjust this path to your SSH key location
  #   environment:
  #     - NEO4J_USER=neo4j
  #     - NEO4J_PASSWORD=password
  #     - NEO4J_URI=bolt://neo4j:7687
  #     - POSTGIS_USER=gis
  #     - POSTGIS_PASSWORD=password
  #     - POSTGIS_HOST=postgis
  #     - POSTGIS_PORT=5432
  #   depends_on:
  #     neo4j:
  #       condition: service_healthy
  #     postgis:
  #       condition: service_healthy
  #   secrets:
  #     - udl_user
  #     - udl_password

  omnicat-welders-arc-ingestor:
    image: omnicat-welders-arc-ingestor:latest
    build:
      context: .
      dockerfile: Dockerfile
      target: welders-arc-ingestor 
      ssh:
        - default=C:/Users/<USER>/.ssh/id_rsa  # Adjust this path to your SSH key location
    environment:
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=password
      - NEO4J_URI=bolt://neo4j:7687
      - POSTGIS_USER=gis
      - POSTGIS_PASSWORD=password
      - POSTGIS_HOST=postgis
      - POSTGIS_PORT=5432
    depends_on:
      neo4j:
        condition: service_healthy
      postgis:
        condition: service_healthy
    secrets:
      - welders_arc_kafka_sasl_plain_username
      - welders_arc_kafka_sasl_plain_password
      - udl_user
      - udl_password

volumes:
  neo4j_data:
  neo4j_logs:
  neo4j_import:
  postgis_data:

secrets:
  udl_user:
    file: ./secrets/udl_user
  udl_password:
    file: ./secrets/udl_password
  welders_arc_kafka_sasl_plain_username:
    file: ./secrets/welders_arc_kafka_sasl_plain_username
  welders_arc_kafka_sasl_plain_password:
    file: ./secrets/welders_arc_kafka_sasl_plain_password