import numpy as np
from datetime import datetime
from typing import Protocol, Tuple
from astropy.coordinates import ITRS, CartesianRepresentation, SphericalRepresentation
from astropy import units as u
import pymap3d as pm
import cupynumeric as cnp


class EcefToGeodeticProtocol(Protocol):
    @staticmethod
    def transform_position(ecef_pos: np.ndarray) -> np.ndarray:
        """Transform ECEF position to geodetic coordinates.

        Args:
            ecef_pos: array of shape (n, 3) containing ECEF position vectors in kilometers [x, y, z]

        Returns:
            array of shape (n, 3) containing geodetic coordinates [latitude, longitude, altitude]
            where latitude and longitude are in degrees and altitude is in kilometers.
            Any input row containing NaN values will result in NaN values in the corresponding output row.
        """
        ...


class EcefToGeodeticAstropy(EcefToGeodeticProtocol):
    @staticmethod
    def _transform_core(positions: np.ndarray) -> np.ndarray:
        """Core transformation logic for converting ECEF to geodetic coordinates.

        Args:
            positions: Array of ECEF positions with units in kilometers

        Returns:
            Array of geodetic coordinates [latitude, longitude, altitude]
            where latitude and longitude are in degrees and altitude is in kilometers
        """
        # Convert positions to Astropy units (km)
        ecef_pos_with_units = positions * u.km
        x = ecef_pos_with_units[:, 0]
        y = ecef_pos_with_units[:, 1]
        z = ecef_pos_with_units[:, 2]

        # Create CartesianRepresentation for all positions
        cartesian = CartesianRepresentation(x, y, z)

        # Convert to ITRS coordinates
        itrs = ITRS(cartesian)

        # Get Earth locations from ITRS
        locs = itrs.earth_location

        # Convert directly to geodetic
        geodetic = locs.to_geodetic()

        # Extract components
        lat = geodetic.lat.deg
        lon = geodetic.lon.deg
        height = geodetic.height.to(u.km).value

        # Normalize longitude to [-180, 180] range
        lon = np.where(lon > 180, lon - 360, lon)
        # Convert any +180 to -180 to maintain half-open interval [-180, 180)
        lon = np.where(lon == 180, -180, lon)

        # Return as numpy array
        return np.column_stack([lat, lon, height])

    @staticmethod
    def transform_position(ecef_pos: np.ndarray) -> np.ndarray:
        """Transform ECEF position to geodetic coordinates using Astropy.

        Args:
            ecef_pos: array of shape (n, 3) containing ECEF position vectors in kilometers [x, y, z]

        Returns:
            array of shape (n, 3) containing geodetic coordinates [latitude, longitude, altitude]
            where latitude and longitude are in degrees and altitude is in kilometers.
            Any input row containing NaN values will result in NaN values in the corresponding output row.
        """
        # Find rows with any NaN values
        nan_mask = np.isnan(ecef_pos).any(axis=1)

        # If all values are NaN, return array of NaNs
        if np.all(nan_mask):
            return np.full_like(ecef_pos, np.nan)

        # If there are some NaN values, process only valid rows
        if np.any(nan_mask):
            valid_pos = ecef_pos[~nan_mask]
        else:
            valid_pos = ecef_pos

        # Use core transformation logic
        valid_geodetic_pos = EcefToGeodeticAstropy._transform_core(valid_pos)

        # If there were any NaN values, reconstruct the full output array
        if np.any(nan_mask):
            geodetic_pos = np.full_like(ecef_pos, np.nan)
            geodetic_pos[~nan_mask] = valid_geodetic_pos
            return geodetic_pos

        return valid_geodetic_pos


class EcefToGeodeticPyMap3D(EcefToGeodeticProtocol):
    @staticmethod
    def _transform_core(positions: np.ndarray) -> np.ndarray:
        """Core transformation logic for converting ECEF to geodetic coordinates.

        Args:
            positions: Array of ECEF positions in kilometers

        Returns:
            Array of geodetic coordinates [latitude, longitude, altitude]
            where latitude and longitude are in degrees and altitude is in kilometers
        """
        # Extract ECEF coordinates (x, y, z) in km
        x = positions[:, 0]
        y = positions[:, 1]
        z = positions[:, 2]

        # Convert ECEF to geodetic using PyMap3D
        # PyMap3D takes input in meters, so we convert from km
        lat, lon, alt = pm.ecef2geodetic(x * 1000, y * 1000, z * 1000)

        # Normalize longitude to [-180, 180] range
        lon = np.where(lon > 180, lon - 360, lon)
        # Convert any +180 to -180 to maintain half-open interval [-180, 180)
        lon = np.where(lon == 180, -180, lon)

        # Combine results and return (altitude back to km)
        return np.column_stack([lat, lon, alt / 1000])

    @staticmethod
    def transform_position(ecef_pos: np.ndarray) -> np.ndarray:
        """Transform ECEF position to geodetic coordinates using PyMap3D.

        Args:
            ecef_pos: array of shape (n, 3) containing ECEF position vectors in kilometers [x, y, z]

        Returns:
            array of shape (n, 3) containing geodetic coordinates [latitude, longitude, altitude]
            where latitude and longitude are in degrees and altitude is in kilometers.
            Any input row containing NaN values will result in NaN values in the corresponding output row.
        """
        # Find rows with any NaN values
        nan_mask = np.isnan(ecef_pos).any(axis=1)

        # If all values are NaN, return array of NaNs
        if np.all(nan_mask):
            return np.full_like(ecef_pos, np.nan)

        # If there are some NaN values, process only valid rows
        if np.any(nan_mask):
            valid_pos = ecef_pos[~nan_mask]
        else:
            valid_pos = ecef_pos

        # Use core transformation logic
        valid_geodetic_pos = EcefToGeodeticPyMap3D._transform_core(valid_pos)

        # If there were any NaN values, reconstruct the full output array
        if np.any(nan_mask):
            geodetic_pos = np.full_like(ecef_pos, np.nan)
            geodetic_pos[~nan_mask] = valid_geodetic_pos
            return geodetic_pos

        return valid_geodetic_pos


class EcefToGeodeticLegate(EcefToGeodeticProtocol):
    """ECEF to geodetic conversion using Legate's cupynumeric for GPU acceleration.
    
    This implementation uses the same algorithm as PyMap3D (WGS84 ellipsoid) but with
    GPU acceleration using cupynumeric arrays.
    """
    
    # WGS84 ellipsoid constants
    WGS84_A = 6378.137  # semi-major axis (km)
    WGS84_B = 6356.752314245  # semi-minor axis (km)
    WGS84_E2 = 0.00669437999014132  # eccentricity squared

    @staticmethod
    def _transform_core(positions: cnp.ndarray) -> cnp.ndarray:
        """Core transformation logic for converting ECEF to geodetic coordinates on GPU.

        This implementation follows the same algorithm as PyMap3D but uses cupynumeric
        arrays for GPU acceleration.

        Args:
            positions: CuPy array of ECEF positions in kilometers

        Returns:
            CuPy array of geodetic coordinates [latitude, longitude, altitude]
            where latitude and longitude are in degrees and altitude is in kilometers
        """
        x = positions[:, 0]
        y = positions[:, 1]
        z = positions[:, 2]

        # Calculate longitude
        lon = cnp.arctan2(y, x) * 180.0 / cnp.pi

        # Normalize longitude to [-180, 180] range
        lon = cnp.where(lon > 180, lon - 360, lon)
        lon = cnp.where(lon == 180, -180, lon)  # +180 -> -180 for half-open interval

        # Calculate auxiliary values for latitude and altitude
        p = cnp.sqrt(x**2 + y**2)
        theta = cnp.arctan2(z * EcefToGeodeticLegate.WGS84_A, 
                           p * EcefToGeodeticLegate.WGS84_B)

        # Calculate latitude iteratively using the same algorithm as PyMap3D
        lat = cnp.arctan2(
            z + EcefToGeodeticLegate.WGS84_E2 * EcefToGeodeticLegate.WGS84_B * cnp.sin(theta)**3,
            p - EcefToGeodeticLegate.WGS84_E2 * EcefToGeodeticLegate.WGS84_A * cnp.cos(theta)**3
        )

        # Convert latitude to degrees
        lat = lat * 180.0 / cnp.pi

        # Calculate N - radius of curvature in the prime vertical
        N = EcefToGeodeticLegate.WGS84_A / cnp.sqrt(
            1 - EcefToGeodeticLegate.WGS84_E2 * cnp.sin(lat * cnp.pi / 180.0)**2
        )

        # Calculate altitude in kilometers
        alt = p / cnp.cos(lat * cnp.pi / 180.0) - N

        # Special case for poles where p -> 0
        pole_mask = p < 1e-6  # Close to poles
        if cnp.any(pole_mask):
            # For poles, altitude is simply distance from center minus polar radius
            r = cnp.sqrt(x**2 + y**2 + z**2)
            alt = cnp.where(pole_mask, r - EcefToGeodeticLegate.WGS84_B, alt)
            # Latitude at exact poles
            lat = cnp.where(pole_mask, cnp.sign(z) * 90.0, lat)

        # Combine results into a single array
        return cnp.column_stack([lat, lon, alt])

    @staticmethod
    def transform_position(ecef_pos: np.ndarray) -> np.ndarray:
        """Transform ECEF position to geodetic coordinates using Legate cupynumeric.

        Args:
            ecef_pos: array of shape (n, 3) containing ECEF position vectors in kilometers [x, y, z]

        Returns:
            array of shape (n, 3) containing geodetic coordinates [latitude, longitude, altitude]
            where latitude and longitude are in degrees and altitude is in kilometers.
            Any input row containing NaN values will result in NaN values in the corresponding output row.
        """
        # Find rows with any NaN values
        nan_mask = np.isnan(ecef_pos).any(axis=1)

        # If all values are NaN, return array of NaNs
        if np.all(nan_mask):
            return np.full_like(ecef_pos, np.nan)

        # If there are some NaN values, process only valid rows
        if np.any(nan_mask):
            valid_pos = ecef_pos[~nan_mask]
        else:
            valid_pos = ecef_pos

        # Transfer data to GPU
        valid_pos_gpu = cnp.array(valid_pos)

        # Use GPU-accelerated core transformation
        valid_geodetic_pos_gpu = EcefToGeodeticLegate._transform_core(valid_pos_gpu)

        # Transfer results back to CPU
        valid_geodetic_pos = valid_geodetic_pos_gpu.get() if hasattr(valid_geodetic_pos_gpu, 'get') else np.array(valid_geodetic_pos_gpu)

        # If there were any NaN values, reconstruct the full output array
        if np.any(nan_mask):
            geodetic_pos = np.full_like(ecef_pos, np.nan)
            geodetic_pos[~nan_mask] = valid_geodetic_pos
            return geodetic_pos

        return valid_geodetic_pos
