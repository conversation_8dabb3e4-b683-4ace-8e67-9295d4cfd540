from __future__ import absolute_import


API_KEYS = {
    0: 'Produce',
    1: 'Fetch',
    2: 'ListOffsets',
    3: 'Metadata',
    4: 'LeaderAndIsr',
    5: 'StopReplica',
    6: 'UpdateMetadata',
    7: 'ControlledShutdown',
    8: 'OffsetCommit',
    9: 'OffsetFetch',
    10: 'FindCoordinator',
    11: 'JoinGroup',
    12: 'Heartbeat',
    13: 'LeaveGroup',
    14: 'SyncGroup',
    15: 'DescribeGroups',
    16: 'ListGroups',
    17: 'SaslHandshake',
    18: 'ApiVersions',
    19: 'CreateTopics',
    20: 'DeleteTopics',
    21: 'DeleteRecords',
    22: 'InitProducerId',
    23: 'OffsetForLeaderEpoch',
    24: 'AddPartitionsToTxn',
    25: 'AddOffsetsToTxn',
    26: 'EndTxn',
    27: 'WriteTxnMarkers',
    28: 'TxnOffsetCommit',
    29: 'DescribeAcls',
    30: 'CreateAcls',
    31: 'DeleteAcls',
    32: 'DescribeConfigs',
    33: 'AlterConfigs',
    36: 'SaslAuthenticate',
    37: 'CreatePartitions',
    38: 'CreateDelegationToken',
    39: 'RenewDelegationToken',
    40: 'ExpireDelegationToken',
    41: 'DescribeDelegationToken',
    42: 'DeleteGroups',
    45: 'AlterPartitionReassignments',
    46: 'ListPartitionReassignments',
    48: 'DescribeClientQuotas',
}
