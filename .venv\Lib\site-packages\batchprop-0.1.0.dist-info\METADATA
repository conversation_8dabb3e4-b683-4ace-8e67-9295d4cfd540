Metadata-Version: 2.3
Name: batchprop
Version: 0.1.0
Summary: Batch Propagation Service
License: LICENSE.txt
Author: <PERSON>
Author-email: <EMAIL>
Requires-Python: >=3.12,<3.13
Classifier: License :: Other/Proprietary License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.12
Requires-Dist: astropy (>=7.0.1,<8.0.0)
Requires-Dist: fastapi[all] (>=0.115.8,<0.116.0)
Requires-Dist: numpy (>=2.2.4,<3.0.0)
Requires-Dist: orbitalopsengine @ git+ssh://**************************/space-superiority-sandbox/ngsx/engine/OrbitalOpsEngine.git@main
Requires-Dist: pytest (>=8.3.4,<9.0.0)
Requires-Dist: pytest-benchmark (>=5.1.0,<6.0.0)
Requires-Dist: pyyaml (>=6.0.2,<7.0.0)
Requires-Dist: satkit (>=0.5.5,<0.6.0)
Requires-Dist: sgp4 (>=2.24,<3.0)
Description-Content-Type: text/markdown

# BatchProp

The service runs batch propagation jobs.

![Project Logo](batchprop.webp)

## Quick Start
```bash
poetry install
poetry shell
```

## Run the Web App
```bash
poetry shell
fastapi run src/batchprop/api/http/main.py
```

## Run the Unit Tests
```bash
poetry shell
pytest tests
```

## Run the Benchmarks
```bash
poetry shell
pytest benchmarks
```

## GPU-Accelerated Benchmarks with Legate
The project includes GPU-accelerated implementations using Legate's cupynumeric library for both coordinate transformations and propagation. These implementations can provide significant performance improvements for large batches of data.

### Legate Setup
Ensure you have a compatible GPU and the Legate environment is properly installed:

```bash
# Check if Legate is available
python -c "import cupynumeric as cnp; print(f'Legate version: {cnp.__version__}')"
```

### Running GPU Benchmarks
To run benchmarks using GPU acceleration:

```bash
# Run with single GPU
legate --gpus 1 pytest benchmarks/test_eci_to_ecef_benchmark.py

# Run with specific tests (e.g., only Legate implementation)
legate --gpus 1 pytest benchmarks/test_eci_to_ecef_benchmark.py -k EciToEcefLegate

# Control GPU memory allocation (4GB in this example)
legate --gpus 1 -ll:gsize 4096 pytest benchmarks/test_eci_to_ecef_benchmark.py

# Use both CPU and GPU resources
legate --gpus 1 --cpus 8 pytest benchmarks/test_eci_to_ecef_benchmark.py
```

### Performance Optimization Tips
For optimal performance with Legate:

1. Process data in large batches when possible (e.g., 1000+ positions)
2. Keep data on the GPU for as long as possible to minimize transfer overhead
3. Adjust the batch size based on your GPU memory capacity
4. When testing larger workloads (10k+ positions), increase GPU memory with `-ll:gsize`
5. Run with the profiler to identify bottlenecks: `-ll:prof 1`

### Profiling
To identify performance bottlenecks:

```bash
legate --gpus 1 -ll:prof 1 pytest benchmarks/test_eci_to_ecef_benchmark.py -k EciToEcefLegate
```

## Future Work
A few SGP4 implementations for batch propagation are implemented and tested.

There are other ones out there that might be worth exploring:
  - [SPICE](https://naif.jpl.nasa.gov/naif/toolkit.html) and [spiceypy](https://github.com/AndrewAnnex/spiceypy)
  - [GMAT](https://github.com/GmatCentral/gmat-central)
  - [Skyfield](https://bluescarni.github.io/heyoka.py/notebooks/sgp4_propagator.html)
  - [Poliastro](https://docs.poliastro.space/en/stable/index.html)
  - [Orekit](https://orekit.org/)
  - [Astropy](https://docs.astropy.org/en/stable/index.html)

.. as well as some [other wrappers on python-sgp4](https://www.opensourcesatellite.org/open-source-solutions-multi-satellite-orbit-propagation-challenge/), including a [numba wrapper](https://github.com/astrojuanlu/python-sgp4/tree/the-return-of-numba) and a [numpy vectorization wrapper](https://github.com/enritoomey/python-sgp4/tree/master).

These libraries may also allow for vectorized or parallel coordinate transformations.
