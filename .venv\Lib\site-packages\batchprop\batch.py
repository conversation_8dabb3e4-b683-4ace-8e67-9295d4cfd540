from datetime import datetime
from typing import Iterator

import numpy as np

from batchprop import TLE


class PropagatedTleBatch:
    """
    A class to encapsulate a batch of propagated TLE data.

    This class provides an intuitive interface for working with propagation results,
    which are stored as a (n_tles, n_times, 3) array of position or velocity vectors.

    Attributes:
        data (np.ndarray): Array of shape (n_tles, n_times, 3) containing vector data
        tles (list[TLE]): Optional list of TLE objects corresponding to the first dimension
        times (list[datetime]): Optional list of times corresponding to the second dimension
    """

    def __init__(
        self,
        data: np.ndarray,
        tles: list[TLE] | None = None,
        times: list[datetime] | None = None
    ):
        """
        Initialize a PropagatedTleBatch instance.

        Args:
            data: Array of shape (n_tles, n_times, 3) containing propagated position or velocity vectors
            tles: Optional list of TLE objects corresponding to the first dimension
            times: Optional list of times corresponding to the second dimension
        """
        # Validate shape
        if len(data.shape) != 3 or data.shape[2] != 3:
            raise ValueError(
                f"Data array must have shape (n_tles, n_times, 3), got {data.shape}")

        self.data = data
        self.tles = tles
        self.times = times

        # Validate metadata if provided
        if tles is not None and len(tles) != data.shape[0]:
            raise ValueError(
                f"Number of TLEs ({len(tles)}) must match first dimension of data array ({data.shape[0]})")

        if times is not None and len(times) != data.shape[1]:
            raise ValueError(
                f"Number of times ({len(times)}) must match second dimension of data array ({data.shape[1]})")

    @property
    def n_tles(self) -> int:
        """Number of TLEs in the batch."""
        return self.data.shape[0]

    @property
    def n_times(self) -> int:
        """Number of time points in the batch."""
        return self.data.shape[1]

    def get_tle(self, index: int) -> np.ndarray:
        """
        Get data for a specific TLE across all times.

        Args:
            index: Index of the TLE

        Returns:
            Array with shape (n_times, 3)
        """
        return self.data[index]

    def get_time(self, index: int) -> np.ndarray:
        """
        Get data for all TLEs at a specific time.

        Args:
            index: Index of the time point

        Returns:
            Array with shape (n_tles, 3)
        """
        return self.data[:, index]

    def __getitem__(self, key: int | tuple[int, int]) -> np.ndarray:
        """
        Access batch elements using indexing syntax.

        If a single integer is provided, returns data for that TLE across all times.
        If a tuple of (tle_index, time_index) is provided, returns data for that specific state.

        Examples:
            # Get data for the first TLE across all times
            data = batch[0]  # Shape: (n_times, 3)

            # Get data for the first TLE at the second time point
            data = batch[0, 1]  # Shape: (3,)
        """
        if isinstance(key, int):
            return self.get_tle(key)
        elif isinstance(key, tuple) and len(key) == 2:
            return self.data[key[0], key[1]]
        else:
            raise TypeError(
                f"Invalid index type: {type(key)}. Use an integer for TLE indexing or a tuple (tle_index, time_index) for state indexing")

    def iter_tles(self) -> Iterator[tuple[int, np.ndarray]]:
        """
        Iterate over all TLEs in the batch.

        Yields:
            Tuples of (tle_index, data) where data has shape (n_times, 3)
        """
        for i in range(self.n_tles):
            yield i, self.data[i]

    def iter_times(self) -> Iterator[tuple[int, np.ndarray]]:
        """
        Iterate over all time points in the batch.

        Yields:
            Tuples of (time_index, data) where data has shape (n_tles, 3)
        """
        for i in range(self.n_times):
            yield i, self.data[:, i]
