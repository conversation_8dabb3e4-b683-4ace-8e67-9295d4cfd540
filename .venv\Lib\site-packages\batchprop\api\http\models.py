# generated by fastapi-codegen:
#   filename:  doc/openapi.yaml
#   timestamp: 2025-01-31T22:38:23+00:00

from __future__ import annotations

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import AnyUrl, BaseModel, Field, RootModel, validator


class PropagatorInfo(BaseModel):
    propagator_id: Optional[str] = Field(
        None,
        description="A unique identifier for this propagator (e.g., 'RK4', 'SGP4').",
    )
    description: Optional[str] = Field(
        None, description='A short description of the propagator.'
    )
    accepted_metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Keys and descriptions of metadata fields this propagator uses (e.g., 'step_size').",
    )
    required_record_data: Optional[List[str]] = Field(
        None,
        description="List of record-specific parameters this propagator expects in the 'data' field (e.g., 'B*').",
    )


class Float3(RootModel[List[float]]):
    """
    An array of three floats representing a 3D vector in kilometers.
    """

    @validator('root')
    def check_length(cls, v):
        if len(v) != 3:
            raise ValueError('The list must contain exactly three floats.')
        return v


class BatchPropResponse(BaseModel):
    run_id: UUID = Field(
        ..., description='Unique identifier for this batch propagation job.'
    )


class Artifact(BaseModel):
    artifact_id: str = Field(..., description='Unique ID for this artifact.')
    type: str = Field(
        ..., description="Type/category of the artifact (e.g., 'STATEVECTORS')."
    )
    timestamp: datetime = Field(
        ..., description='Timestamp when the artifact was created or relevant.'
    )
    url: AnyUrl = Field(...,
                        description='Link to retrieve or download the artifact.')


class EciStateVector(BaseModel):
    position: Float3 = Field(..., description='ECI position (km): [x, y, z]')
    velocity: Float3 = Field(...,
                             description='ECI velocity (km/s): [vx, vy, vz]')
    epoch: datetime = Field(
        ..., description='Timestamp for the given position/velocity data.'
    )


class BatchRecord(BaseModel):
    eci_state_vector: EciStateVector = Field(
        ..., description='Position, velocity in kilometers, plus epoch.'
    )
    data: Optional[Dict[str, Any]] = Field(
        None,
        description='Record-specific parameters required by the propagator (e.g., ballistic coefficient).',
    )


class BatchPropRequest(BaseModel):
    propagator_id: str = Field(
        ..., description="Identifier for the chosen propagator (e.g. 'RK4', 'SGP4')."
    )
    propagator_config: Dict[str, Any] = Field(
        ...,
        description='Additional configuration for the propagator (e.g., step_size for RK4).',
    )
    records: List[BatchRecord] = Field(
        ...,
        description='Up to 1000 records, each containing an ECI state vector and optional data.',
        max_items=1000,
    )
