import json
import pytest
from datetime import datetime
from omnicat.ingestor.welders_arc.models import <PERSON>set_Full, Elset_Sgp4Xp, Elset_Sgp4, Elset_UctCandidate


def test_elset_full_model():
    """Test the Elset model with a complete example JSON."""
    # Example JSON string from the requirements
    example_json_str = """{
        "idElset":"fbaca206-a176-4c61-89a2-69b58acf9f9c",
        "classificationMarking":"U",
        "satNo":43435,
        "epoch":"2025-01-28T17:30:10.014912Z",
        "meanMotion":0.06955812,
        "idOnOrbit":"43435",
        "eccentricity":0.3764494,
        "inclination":53.2896,
        "raan":53.7696,
        "argOfPerigee":112.3476,
        "meanAnomaly":355.8605,
        "revNo":118,
        "bStar":0.0,
        "meanMotionDot":-1.059E-5,
        "meanMotionDDot":0.0,
        "semiMajorAxis":249748.728,
        "period":20702.112,
        "apogee":343766.48600000003,
        "perigee":155730.969,
        "line1":"1 43435U 18038A   25028.72928258 -.00001059 +00000+0 +00000+0 0 99992",
        "line2":"2 43435  53.2896  53.7696 3764494 112.3476 355.8605 00.06955812001189",
        "createdAt":"2025-01-13T15:05:18.412Z",
        "createdBy":"system.ob-ingest",
        "source":"18th SPCS",
        "dataMode":"REAL",
        "algorithm":"SGP4",
        "origNetwork":"OPS1"
    }"""

    # Test deserialization (JSON string to model)
    elset = Elset_Full.model_validate_json(example_json_str)

    # Verify all fields were correctly parsed
    assert elset.idElset == "fbaca206-a176-4c61-89a2-69b58acf9f9c"
    assert elset.classificationMarking == "U"
    assert elset.satNo == 43435
    assert elset.epoch == datetime.fromisoformat(
        "2025-01-28T17:30:10.014912+00:00")
    assert elset.meanMotion == 0.06955812
    assert elset.idOnOrbit == "43435"
    assert elset.eccentricity == 0.3764494
    assert elset.inclination == 53.2896
    assert elset.raan == 53.7696
    assert elset.argOfPerigee == 112.3476
    assert elset.meanAnomaly == 355.8605
    assert elset.revNo == 118
    assert elset.bStar == 0.0
    assert elset.meanMotionDot == -1.059E-5
    assert elset.meanMotionDDot == 0.0
    assert elset.semiMajorAxis == 249748.728
    assert elset.period == 20702.112
    assert elset.apogee == 343766.48600000003
    assert elset.perigee == 155730.969
    assert elset.line1 == "1 43435U 18038A   25028.72928258 -.00001059 +00000+0 +00000+0 0 99992"
    assert elset.line2 == "2 43435  53.2896  53.7696 3764494 112.3476 355.8605 00.06955812001189"
    assert elset.createdAt == datetime.fromisoformat(
        "2025-01-13T15:05:18.412+00:00")
    assert elset.createdBy == "system.ob-ingest"
    assert elset.source == "18th SPCS"
    assert elset.dataMode == "REAL"
    assert elset.algorithm == "SGP4"
    assert elset.origNetwork == "OPS1"


def test_elset_sgp4xp_model():
    """Test the Elset_Sgp4Xp model with a complete example JSON."""
    # Example JSON string from the requirements
    example_json_str = """{
        "idElset": "8b269d82-2e23-4e9a-be01-bf285681a30c",
        "classificationMarking": "U",
        "satNo": "46238",
        "epoch": "2025-04-02T09:47:44.108Z",
        "line1": "1 46238U          25092.40814944 +.00000000  10000 2  00000 0 4 0001",
        "line2": "2 46238  50.6216  95.5509 0536519 321.6601 188.5993 10.3355081600001",
        "createdAt": "2025-04-04T01:53:18.475",
        "createdBy": "producer.pulsar",
        "origin": "COMSPOC",
        "source": "Space Book",
        "ephemType": 4,
        "uct": false,
        "origObjectId": "46238",
        "dataMode": "REAL",
        "algorithm": "SGP4-XP",
        "origNetwork": "SDA TAP"
    }"""

    # Test deserialization (JSON string to model)
    elset = Elset_Sgp4Xp.model_validate_json(example_json_str)

    # Verify all fields were correctly parsed
    assert elset.idElset == "8b269d82-2e23-4e9a-be01-bf285681a30c"
    assert elset.classificationMarking == "U"
    assert elset.satNo == "46238"
    assert elset.epoch == "2025-04-02T09:47:44.108Z"
    assert elset.line1 == "1 46238U          25092.40814944 +.00000000  10000 2  00000 0 4 0001"
    assert elset.line2 == "2 46238  50.6216  95.5509 0536519 321.6601 188.5993 10.3355081600001"
    assert elset.createdAt == "2025-04-04T01:53:18.475"
    assert elset.createdBy == "producer.pulsar"
    assert elset.origin == "COMSPOC"
    assert elset.source == "Space Book"
    assert elset.ephemType == 4
    assert elset.uct == False
    assert elset.origObjectId == "46238"
    assert elset.dataMode == "REAL"
    assert elset.algorithm == "SGP4-XP"
    assert elset.origNetwork == "SDA TAP"


def test_elset_sgp4_model():
    """Test the Elset_Sgp4 model with a complete example JSON."""
    # Example JSON string from the requirements
    example_json_str = """{
        "idElset": "d8884ee4-a511-4d5c-86a4-d761af0f6c6c",
        "satNo": 55841,
        "epoch": "2025-04-04T16:37:42.219264Z",
        "dataMode": "REAL",
        "source": "18th SPCS",
        "classificationMarking": "U"
    }"""

    # Test deserialization (JSON string to model)
    elset = Elset_Sgp4.model_validate_json(example_json_str)

    # Verify all fields were correctly parsed
    assert elset.idElset == "d8884ee4-a511-4d5c-86a4-d761af0f6c6c"
    assert elset.satNo == 55841
    assert elset.epoch == datetime.fromisoformat(
        "2025-04-04T16:37:42.219264+00:00")
    assert elset.dataMode == "REAL"
    assert elset.source == "18th SPCS"
    assert elset.classificationMarking == "U"


def test_elset_uct_candidate_model():
    """Test the Elset_UctCandidate model with a complete example JSON."""
    # Example JSON string from the requirements
    example_json_str = """{
        "idElset": "a3bfb32f-8e72-4ed2-9dbc-f34d4b126093",
        "classificationMarking": "U",
        "satNo": 0,
        "epoch": "2025-04-07T04:30:45.810119Z",
        "meanMotion": 13.441864330570668,
        "idOnOrbit": "00000",
        "eccentricity": 0.0011880089314738305,
        "inclination": 63.503642224313296,
        "raan": 105.88024813776536,
        "argOfPerigee": 92.64774963022589,
        "meanAnomaly": 2.5342379913481854,
        "revNo": 999,
        "bStar": 5e-05,
        "meanMotionDot": 0,
        "meanMotionDDot": 0,
        "semiMajorAxis": 7471.8750356535,
        "period": 107.12799687503359,
        "apogee": 7480.751689930712,
        "perigee": 7462.998381376287,
        "line1": "1 99999U 00000A   25 97.18803020 +.00000073 +00000+0 +50000-5 0 99998",
        "line2": "2 99999  63.5036 105.8802 0011880  92.6477   2.5342 13.44186433000000",
        "sourcedData": [
            "24e03686-15cb-4733-8f67-e78cae237df1",
            "b12c6aa9-a9d8-4ce1-a667-3690d8987be6",
            "0e294668-dd83-447d-8f47-f9a6b3d9420e",
            "7c373c76-1717-4f9a-9807-d94335552c6c",
            "1251b9f4-9d85-414e-a684-2b1e67303eaa",
            "9d588147-4a49-4d6f-bc76-c34879ac3f40",
            "ea23867f-29fd-4939-a822-689d9e581e05",
            "153607ad-209b-4a52-8f01-5db065dafce5",
            "fd526727-7c0d-46ee-a661-8eef098bd0b3",
            "9eacfde0-5443-40b6-a303-c290ecdbbf88",
            "24854bd4-38ba-4d10-ae34-cee5496cb491"
        ],
        "sourcedDataTypes": [
            "ELSET",
            "ELSET",
            "ELSET",
            "ELSET",
            "ELSET",
            "ELSET",
            "ELSET",
            "ELSET",
            "ELSET",
            "ELSET",
            "ELSET"
        ],
        "createdBy": "system.rhea",
        "source": "Rhea Space",
        "dataMode": "TEST",
        "algorithm": "SGP4",
        "origNetwork": "TAPNET"
    }"""

    # Test deserialization (JSON string to model)
    elset = Elset_UctCandidate.model_validate_json(example_json_str)

    # Verify all fields were correctly parsed
    assert elset.idElset == "a3bfb32f-8e72-4ed2-9dbc-f34d4b126093"
    assert elset.classificationMarking == "U"
    assert elset.satNo == 0
    assert elset.epoch == datetime.fromisoformat(
        "2025-04-07T04:30:45.810119+00:00")
    assert elset.meanMotion == 13.441864330570668
    assert elset.idOnOrbit == "00000"
    assert elset.eccentricity == 0.0011880089314738305
    assert elset.inclination == 63.503642224313296
    assert elset.raan == 105.88024813776536
    assert elset.argOfPerigee == 92.64774963022589
    assert elset.meanAnomaly == 2.5342379913481854
    assert elset.revNo == 999
    assert elset.bStar == 5e-05
    assert elset.meanMotionDot == 0
    assert elset.meanMotionDDot == 0
    assert elset.semiMajorAxis == 7471.8750356535
    assert elset.period == 107.12799687503359
    assert elset.apogee == 7480.751689930712
    assert elset.perigee == 7462.998381376287
    assert elset.line1 == "1 99999U 00000A   25 97.18803020 +.00000073 +00000+0 +50000-5 0 99998"
    assert elset.line2 == "2 99999  63.5036 105.8802 0011880  92.6477   2.5342 13.44186433000000"
    assert len(elset.sourcedData) == 11
    assert elset.sourcedData[0] == "24e03686-15cb-4733-8f67-e78cae237df1"
    assert all(data_type == "ELSET" for data_type in elset.sourcedDataTypes)
    assert elset.createdBy == "system.rhea"
    assert elset.source == "Rhea Space"
    assert elset.dataMode == "TEST"
    assert elset.algorithm == "SGP4"
    assert elset.origNetwork == "TAPNET"


def test_elset_uct_candidate_model_with_optional_fields_missing():
    """Test the Elset_UctCandidate model with optional fields missing (based on actual Kafka messages)."""
    # Example JSON string based on actual failing Kafka messages
    example_json_str = """{
        "idElset": "6d104420-90cf-407d-a890-3c78a418cb66",
        "classificationMarking": "U//PR-LSAS-SV",
        "epoch": "2025-05-12T17:24:20.3382624Z",
        "meanMotion": 1.0162375,
        "source": "LSAS",
        "eccentricity": 0.0097096,
        "inclination": 0.1072,
        "raan": 287.765,
        "argOfPerigee": 103.4929,
        "meanAnomaly": 119.126,
        "revNo": 1,
        "bStar": -1828600,
        "meanMotionDot": -0.00023087,
        "meanMotionDDot": 0,
        "semiMajorAxis": 41789.9327545782,
        "period": 1416.991598912656,
        "apogee": 42195.696285652055,
        "perigee": 41384.169223504345,
        "line1": "1 99999U          25132.72523591 -.00023087  00000-0 -18286+7 0 00019",
        "line2": "2 99999 000.1072 287.7650 0097096 103.4929 119.1260 01.01623750000017",
        "dataMode": "REAL",
        "algorithm": "ODTK",
        "sourcedData": ["03f39f2e-318e-431e-8e3f-33c57ca77df7"],
        "sourcedDataTypes": ["EO"]
    }"""

    # Test deserialization (JSON string to model) - should work now with optional fields
    elset = Elset_UctCandidate.model_validate_json(example_json_str)

    # Verify required fields were correctly parsed
    assert elset.idElset == "6d104420-90cf-407d-a890-3c78a418cb66"
    assert elset.classificationMarking == "U//PR-LSAS-SV"
    assert elset.epoch == datetime.fromisoformat(
        "2025-05-12T17:24:20.3382624+00:00")
    assert elset.meanMotion == 1.0162375
    assert elset.source == "LSAS"
    assert elset.dataMode == "REAL"
    assert elset.algorithm == "ODTK"

    # Verify optional fields are None when not provided
    assert elset.satNo is None
    assert elset.idOnOrbit is None
    assert elset.createdBy is None
    assert elset.origNetwork is None

    # Verify getIdOnOrbit() returns None when idOnOrbit is None
    assert elset.getIdOnOrbit() is None
