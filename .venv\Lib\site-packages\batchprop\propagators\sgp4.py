from datetime import datetime, timezone, timedelta
from sgp4.api import Satre<PERSON>, jday, SatrecArray
import numpy as np
from multiprocessing import Pool
from batchprop import TLE
import satkit as sk
from typing import Protocol
# import torch
# import dsgp4
# import cupynumeric as cnp

from batchprop.batch import PropagatedTleBatch

from typing import Sequence, Tuple

class PropagatorProtocol(Protocol):
    """Protocol defining the interface for satellite propagators.

    This protocol specifies how propagators should handle multiple TLEs
    and return their ephemerides.
    """

    @staticmethod
    def propagate(tles: list[TLE], times: list[datetime]) -> tuple[PropagatedTleBatch, PropagatedTleBatch]:
        """Propagate multiple TLEs across specified times.

        Args:
            tles: List of TLE objects to propagate
            times: List of datetime objects to propagate for (must be timezone-aware)

        Returns:
            tuple of (position_batch, velocity_batch) where:
                position_batch: PropagatedTleBatch with positions data
                velocity_batch: PropagatedTleBatch with velocities data

                Each batch contains data of shape (n, m, 3) where:
                    n = number of TLEs
                    m = number of time points

                Position data contains [x, y, z] in km
                Velocity data contains [vx, vy, vz] in km/s
        """
        ...


def _datetime_array_to_jday(valid_times: list[datetime]) -> tuple[np.ndarray, np.ndarray]:
    """Convert an array of datetime objects to Julian dates.

    Args:
        valid_times: List of datetime objects with timezone info

    Returns:
        Tuple of (jd, fr) arrays for use with sgp4
    """
    years = np.array([t.year for t in valid_times])
    months = np.array([t.month for t in valid_times])
    days = np.array([t.day for t in valid_times])
    hours = np.array([t.hour for t in valid_times])
    minutes = np.array([t.minute for t in valid_times])
    seconds = np.array([t.second + t.microsecond/1e6 for t in valid_times])

    return jday(years, months, days, hours, minutes, seconds)


def propagate_single(args: tuple[TLE, list[datetime]]) -> tuple[np.ndarray, np.ndarray]:
    """Helper function to propagate a single TLE across multiple times.

    Args:
        args: Tuple of (tle, times) where:
            tle: TLE object to propagate
            times: List of datetime objects

    Returns:
        Tuple of (positions, velocities) arrays for this TLE
    """
    tle, times = args
    n_times = len(times)
    positions = np.full((n_times, 3), np.nan)
    velocities = np.full((n_times, 3), np.nan)

    satellite = Satrec.twoline2rv(tle.line1, tle.line2)

    # Create mask for times with timezone info
    tz_mask = np.array([t.tzinfo is not None for t in times])
    valid_times = np.array(times)[tz_mask]

    if len(valid_times) == 0:
        return positions, velocities

    jd, fr = _datetime_array_to_jday(valid_times)

    # Batch propagate for all times
    err, pos, vel = satellite.sgp4_array(jd, fr)

    # If any error occurred, return all NaNs for this TLE
    if np.any(err != 0):
        return positions, velocities

    # Only keep results where err == 0
    valid_results = err == 0
    valid_indices = np.where(tz_mask)[0][valid_results]

    positions[valid_indices] = pos[valid_results]
    velocities[valid_indices] = vel[valid_results]

    return positions, velocities


class Sgp4Vectorized(PropagatorProtocol):
    """SGP4 implementation using SatrecArray for fully vectorized propagation across all TLEs and times."""

    @staticmethod
    def propagate(tles: list[TLE], times: list[datetime]) -> tuple[PropagatedTleBatch, PropagatedTleBatch]:
        n_tles = len(tles)
        n_times = len(times)
        positions = np.full((n_tles, n_times, 3), np.nan)
        velocities = np.full((n_tles, n_times, 3), np.nan)

        # Create mask for times with timezone info
        tz_mask = np.array([t.tzinfo is not None for t in times])
        valid_times = np.array(times)[tz_mask]

        if len(valid_times) == 0:
            return PropagatedTleBatch(positions, tles, times), PropagatedTleBatch(velocities, tles, times)

        jd, fr = _datetime_array_to_jday(valid_times)
        
        # First create individual Satrec objects
        satellites = []
        for tle in tles:
            satellites.append(Satrec.twoline2rv(tle.line1, tle.line2))
        
        # Create a SatrecArray from the list of Satrec objects
        satrec_array = SatrecArray(satellites)
        
        # Propagate all satellites at all times in a single operation
        err, pos, vel = satrec_array.sgp4(jd, fr)
        
        # Maps valid_times back to the original times array
        valid_indices = np.where(tz_mask)[0]
        
        if len(valid_indices) > 0:
            # For each TLE
            for i in range(n_tles):
                # Get indices where propagation succeeded for this TLE
                success_mask = err[i] == 0
                
                if not np.any(success_mask):
                    # If all propagations failed for this TLE, leave NaNs
                    continue
                
                # Map back to original time indices
                original_indices = valid_indices[success_mask]
                
                # Store positions and velocities for successful propagations
                positions[i, original_indices] = pos[i, success_mask]
                velocities[i, original_indices] = vel[i, success_mask]
        
        return PropagatedTleBatch(positions, tles, times), PropagatedTleBatch(velocities, tles, times)


class Sgp4MultiProcess(PropagatorProtocol):
    """SGP4 multiprocessing implementation of the PropagatorProtocol."""

    @staticmethod
    def propagate(tles: list[TLE], times: list[datetime]) -> tuple[PropagatedTleBatch, PropagatedTleBatch]:
        n_tles = len(tles)
        n_times = len(times)

        # Create list of (tle, times) tuples for each TLE
        propagation_args = [(tle, times) for tle in tles]

        # Use multiprocessing pool to parallelize across TLEs
        with Pool() as pool:
            results = pool.map(propagate_single, propagation_args)

        # Combine results into final arrays
        positions = np.full((n_tles, n_times, 3), np.nan)
        velocities = np.full((n_tles, n_times, 3), np.nan)

        for i, (pos, vel) in enumerate(results):
            positions[i] = pos
            velocities[i] = vel

        return PropagatedTleBatch(positions, tles, times), PropagatedTleBatch(velocities, tles, times)


class Sgp4Satkit(PropagatorProtocol):
    """SGP4 implementation of the PropagatorProtocol using Satkit."""

    @staticmethod
    def propagate(tles: list[TLE], times: list[datetime]) -> tuple[PropagatedTleBatch, PropagatedTleBatch]:
        """Propagate multiple TLEs across specified times using satkit's native parallel propagation.

        Args:
            tles: List of TLE objects to propagate
            times: List of datetime objects to propagate for

        Returns:
            tuple of (position_batch, velocity_batch) where:
                position_batch: PropagatedTleBatch with positions data
                velocity_batch: PropagatedTleBatch with velocities data

                Each batch contains data of shape (n, m, 3) where:
                    n = number of TLEs
                    m = number of time points

                Position data contains [x, y, z] in km
                Velocity data contains [vx, vy, vz] in km/s
        """
        # see example at https://satellite-toolkit.readthedocs.io/latest/examples/Satellite%20Ground%20Contacts.html

        try:
            satkit_tles, satkit_times = Sgp4Satkit.get_satkit_batch(
                tles, times)

            # Vectorized propagation (already optimized in satkit)
            positions, velocities, errors = sk.sgp4(
                satkit_tles, satkit_times, errflag=True)

            # Vectorized unit conversion and reshaping
            positions = (positions / 1000.0).reshape(-1, len(times), 3)
            velocities = (velocities / 1000.0).reshape(-1, len(times), 3)

            # Handle propagation errors for individual TLEs
            if errors is not None:
                # Reshape errors to match the TLE dimension
                error_mask = errors.reshape(len(tles), len(times))
                # For each TLE, if it has any errors, mask all its positions/velocities
                for i in range(len(tles)):
                    if error_mask[i].any():
                        positions[i] = np.nan
                        velocities[i] = np.nan

            # Handle timezone errors
            naive_mask = np.array(
                [t.tzinfo is None for t in times], dtype=bool)
            if naive_mask.any():
                positions[:, naive_mask] = np.nan
                velocities[:, naive_mask] = np.nan

            return PropagatedTleBatch(positions, tles, times), PropagatedTleBatch(velocities, tles, times)

        except BaseException:
            # If there's a catastrophic error (e.g., invalid input format), return all NaNs
            return (
                PropagatedTleBatch(
                    np.full((len(tles), len(times), 3), np.nan), tles, times),
                PropagatedTleBatch(
                    np.full((len(tles), len(times), 3), np.nan), tles, times)
            )

    @staticmethod
    def get_satkit_batch(tles: list[TLE], times: list[datetime]) -> tuple[list[sk.TLE], np.ndarray]:
        satkit_tles = Sgp4Satkit.get_satkit_tles(tles)
        satkit_times = np.array(times)
        return satkit_tles, satkit_times

    @staticmethod
    def get_satkit_tles(tles: list[TLE]) -> list[sk.TLE]:
        satkit_tle_lines = [item for tle in tles for item in (
            tle.line0, tle.line1, tle.line2)]
        satkit_tles = sk.TLE.from_lines(satkit_tle_lines)
        return satkit_tles


# def _dsgp4_tle_batch(tles: list[TLE], times: list[datetime]) -> list[dsgp4.tle.TLE]:
#     try:
#         dsgp4_tles = [
#             dsgp4.tle.TLE([tle.line0, tle.line1, tle.line2])
#             for tle in tles
#         ]
#     except Exception as e:
#         raise ValueError(f"Error creating dsgp4 TLE: {e}")
#     tles_batch = [tle for tle in dsgp4_tles for _ in range(len(times))]
#     return tles_batch


# def _dsgp4_times_batch(tles: list[TLE], times: list[datetime]) -> torch.Tensor:
#     times_tensors = [
#         torch.tensor(
#             [(t - tle.epoch).total_seconds() / 60.0 for t in times])
#         for tle in tles
#     ]
#     return torch.cat(times_tensors)


# def _dsgp4_output_to_numpy(out_batched: torch.Tensor, device: str) -> tuple[np.ndarray, np.ndarray]:
#     # Handle special case: for single TLE + single time, dsgp4 returns [2,3] instead of [1,2,3]
#     if len(out_batched.shape) == 2 and out_batched.shape[0] == 2:
#         # Reshape to add batch dimension
#         out_batched = out_batched.unsqueeze(0)  # Now [1,2,3]

#     # Move tensors to CPU if they're on GPU
#     if device == "cuda":
#         positions = out_batched[:, 0, :].cpu().numpy()
#         velocities = out_batched[:, 1, :].cpu().numpy()
#     else:
#         positions = out_batched[:, 0, :].numpy()
#         velocities = out_batched[:, 1, :].numpy()

#     return positions, velocities


# def _propagate_dsgp4(tles: list[TLE], times: list[datetime], device: str) -> tuple[PropagatedTleBatch, PropagatedTleBatch]:
#     """Common implementation for dsgp4 propagation used by CPU and GPU propagators.

#     Args:
#         tles: List of TLE objects to propagate
#         times: List of datetime objects to propagate for
#         device: Device to use ("cpu" or "cuda")

#     Returns:
#         tuple of (position_batch, velocity_batch)
#     """
#     torch.set_default_device(device)

#     tles_batch = _dsgp4_tle_batch(tles, times)

#     times_batch = _dsgp4_times_batch(tles, times)

#     out_batched = dsgp4.propagate_batch(
#         tles_batch, times_batch, initialized=False)

#     positions, velocities = _dsgp4_output_to_numpy(out_batched, device)

#     positions = positions.reshape(len(tles), len(times), 3)
#     velocities = velocities.reshape(len(tles), len(times), 3)

#     return PropagatedTleBatch(positions, tles, times), PropagatedTleBatch(velocities, tles, times)


# class Dsgp4Cpu(PropagatorProtocol):
#     """SGP4 implementation using the dsgp4 differentiable propagator library."""

#     @staticmethod
#     def propagate(tles: list[TLE], times: list[datetime]) -> tuple[PropagatedTleBatch, PropagatedTleBatch]:
#         """Propagate multiple TLEs across specified times using dsgp4 differentiable propagation.

#         Args:
#             tles: List of TLE objects to propagate
#             times: List of datetime objects to propagate for (must be timezone-aware)

#         Returns:
#             tuple of (position_batch, velocity_batch) where:
#                 position_batch: PropagatedTleBatch with positions data
#                 velocity_batch: PropagatedTleBatch with velocities data

#                 Each batch contains data of shape (n, m, 3) where:
#                     n = number of TLEs
#                     m = number of time points

#                 Position data contains [x, y, z] in km
#                 Velocity data contains [vx, vy, vz] in km/s
#         """
#         return _propagate_dsgp4(tles, times, "cpu")


# class Dsgp4Gpu(PropagatorProtocol):
#     """SGP4 implementation using the dsgp4 differentiable propagator library."""

#     @staticmethod
#     def propagate(tles: list[TLE], times: list[datetime]) -> tuple[PropagatedTleBatch, PropagatedTleBatch]:
#         """Propagate multiple TLEs across specified times using dsgp4 differentiable propagation.

#         Args:
#             tles: List of TLE objects to propagate
#             times: List of datetime objects to propagate for (must be timezone-aware)

#         Returns:
#             tuple of (position_batch, velocity_batch) where:
#                 position_batch: PropagatedTleBatch with positions data
#                 velocity_batch: PropagatedTleBatch with velocities data

#                 Each batch contains data of shape (n, m, 3) where:
#                     n = number of TLEs
#                     m = number of time points

#                 Position data contains [x, y, z] in km
#                 Velocity data contains [vx, vy, vz] in km/s
#         """
#         return _propagate_dsgp4(tles, times, "cuda")


# class Sgp4Legate(PropagatorProtocol):
#     """SGP4 implementation using SatrecArray with Legate cupynumeric for GPU acceleration."""

#     @staticmethod
#     def propagate(tles: list[TLE], times: list[datetime]) -> tuple[PropagatedTleBatch, PropagatedTleBatch]:
#         n_tles = len(tles)
#         n_times = len(times)
#         positions = np.full((n_tles, n_times, 3), np.nan)
#         velocities = np.full((n_tles, n_times, 3), np.nan)

#         # Create mask for times with timezone info
#         tz_mask = np.array([t.tzinfo is not None for t in times])
#         valid_times = np.array(times)[tz_mask]

#         if len(valid_times) == 0:
#             return PropagatedTleBatch(positions, tles, times), PropagatedTleBatch(velocities, tles, times)

#         # Convert datetime arrays to jday format
#         years = cnp.array([t.year for t in valid_times])
#         months = cnp.array([t.month for t in valid_times])
#         days = cnp.array([t.day for t in valid_times])
#         hours = cnp.array([t.hour for t in valid_times])
#         minutes = cnp.array([t.minute for t in valid_times])
#         seconds = cnp.array([t.second + t.microsecond/1e6 for t in valid_times])
        
#         # Use jday with cupynumeric arrays
#         jd, fr = jday(years, months, days, hours, minutes, seconds)
        
#         # First create individual Satrec objects
#         satellites = []
#         for tle in tles:
#             satellites.append(Satrec.twoline2rv(tle.line1, tle.line2))
        
#         # Create a SatrecArray from the list of Satrec objects
#         satrec_array = SatrecArray(satellites)
        
#         # Propagate all satellites at all times in a single operation
#         err, pos, vel = satrec_array.sgp4(jd, fr)
        
#         # Convert results to numpy arrays for post-processing
#         err_np = cnp.asarray(err).get() if hasattr(err, 'get') else np.asarray(err)
#         pos_np = cnp.asarray(pos).get() if hasattr(pos, 'get') else np.asarray(pos)
#         vel_np = cnp.asarray(vel).get() if hasattr(vel, 'get') else np.asarray(vel)
        
#         # Maps valid_times back to the original times array
#         valid_indices = np.where(tz_mask)[0]
        
#         if len(valid_indices) > 0:
#             # For each TLE
#             for i in range(n_tles):
#                 # Get indices where propagation succeeded for this TLE
#                 success_mask = err_np[i] == 0
                
#                 if not np.any(success_mask):
#                     # If all propagations failed for this TLE, leave NaNs
#                     continue
                
#                 # Map back to original time indices
#                 original_indices = valid_indices[success_mask]
                
#                 # Store positions and velocities for successful propagations
#                 positions[i, original_indices] = pos_np[i, success_mask]
#                 velocities[i, original_indices] = vel_np[i, success_mask]
        
#         return PropagatedTleBatch(positions, tles, times), PropagatedTleBatch(velocities, tles, times)